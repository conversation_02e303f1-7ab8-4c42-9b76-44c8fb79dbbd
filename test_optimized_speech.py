#!/usr/bin/env python3
"""
Test script for the optimized Whisper Medium.en speech processor
"""

import sys
import time
import numpy as np
from speech_processor import SpeechProcessor

def test_basic_functionality():
    """Test basic speech processor functionality"""
    print("Testing Whisper Medium.en Speech Processor...")
    print("=" * 50)
    
    try:
        # Initialize processor
        print("1. Initializing speech processor...")
        processor = SpeechProcessor()
        print("✓ Speech processor initialized successfully")
        
        # Test audio generation
        print("\n2. Testing with synthetic audio...")
        sample_rate = 16000
        duration = 3  # 3 seconds
        
        # Generate a simple sine wave (simulating speech frequency)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        frequency = 440  # A4 note, within speech range
        audio = np.sin(2 * np.pi * frequency * t).astype(np.float32) * 0.3
        
        print(f"Generated test audio: {len(audio)} samples, {duration}s duration")
        
        # Test transcription
        print("\n3. Testing transcription...")
        start_time = time.time()
        
        try:
            result = processor.speech_to_text(audio)
            transcription_time = time.time() - start_time
            
            print(f"✓ Transcription completed in {transcription_time:.2f} seconds")
            print(f"Result: '{result}'")
            
            # Calculate real-time factor
            rtf = transcription_time / duration
            print(f"Real-time factor: {rtf:.2f}x")
            
            if rtf < 1.0:
                print("✓ Faster than real-time processing achieved!")
            else:
                print("⚠️  Processing slower than real-time")
                
        except Exception as e:
            print(f"✗ Transcription failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def test_audio_recording():
    """Test audio recording functionality"""
    print("\n" + "=" * 50)
    print("Testing Audio Recording...")
    
    try:
        processor = SpeechProcessor()
        
        print("Testing 2-second recording...")
        print("Speak now!")
        
        audio = processor.record_audio(duration=2)
        
        if audio is not None and len(audio) > 0:
            print(f"✓ Recording successful: {len(audio)} samples")
            
            # Test transcription of recorded audio
            print("Transcribing recorded audio...")
            result = processor.speech_to_text(audio)
            print(f"Transcription: '{result}'")
            
            return True
        else:
            print("✗ Recording failed or returned empty audio")
            return False
            
    except Exception as e:
        print(f"✗ Recording test failed: {e}")
        return False

def test_pipeline_integration():
    """Test the complete pipeline"""
    print("\n" + "=" * 50)
    print("Testing Complete Pipeline...")
    
    try:
        from chatbot_pipeline import process_audio_file
        from quick_record import record_to_wav
        
        print("Recording 3 seconds of audio for pipeline test...")
        print("Please speak clearly!")
        
        # Record audio
        audio_file = record_to_wav("test_pipeline.wav", seconds=3)
        
        # Process through pipeline
        result = process_audio_file(audio_file, verbose=True)
        
        print(f"\nPipeline result: '{result}'")
        
        # Clean up
        import os
        if os.path.exists(audio_file):
            os.remove(audio_file)
            
        return True
        
    except Exception as e:
        print(f"✗ Pipeline test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Whisper Medium.en Optimization Test Suite")
    print("Optimized for GTX 1650 with maximum accuracy")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Basic functionality
    total_tests += 1
    if test_basic_functionality():
        tests_passed += 1
    
    # Test 2: Audio recording (optional, requires microphone)
    response = input("\nTest audio recording? (requires microphone) [y/N]: ").lower()
    if response in ['y', 'yes']:
        total_tests += 1
        if test_audio_recording():
            tests_passed += 1
    
    # Test 3: Pipeline integration (optional)
    response = input("\nTest complete pipeline? (requires microphone) [y/N]: ").lower()
    if response in ['y', 'yes']:
        total_tests += 1
        if test_pipeline_integration():
            tests_passed += 1
    
    # Results
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Your optimized setup is working perfectly.")
        print("\nRecommended usage:")
        print("  python quick_record.py          # Record audio")
        print("  python chatbot_pipeline.py      # Process with grammar correction")
        print("  python stt_pipeline.py          # Direct transcription")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
