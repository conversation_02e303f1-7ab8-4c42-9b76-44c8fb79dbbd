# quick_record.py
import soundfile as sf
import sounddevice as sd
import numpy as np
import time

def record_to_wav(out_path="clip.wav", seconds=6, samplerate=16000):
    """
    Record audio from microphone to WAV file.
    
    Args:
        out_path: Output file path
        seconds: Recording duration in seconds
        samplerate: Sample rate (16000 Hz recommended for Whisper)
    
    Returns:
        str: Path to the recorded file
    """
    print(f"Recording {seconds}s...")
    audio = sd.rec(int(seconds * samplerate), samplerate=samplerate, channels=1, dtype="float32")
    sd.wait()
    sf.write(out_path, audio, samplerate)
    print("Saved:", out_path)
    return out_path

if __name__ == "__main__":
    p = record_to_wav("clip.wav", seconds=6)
    print("Now run:  python stt_pipeline.py")
