#!/usr/bin/env python3
"""
Cleanup script to remove old distil-whisper models and free up disk space
"""

import os
import shutil
from pathlib import Path

def get_directory_size(path):
    """Calculate total size of directory in MB"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            try:
                total_size += os.path.getsize(filepath)
            except (OSError, FileNotFoundError):
                pass
    return total_size / (1024 * 1024)  # Convert to MB

def main():
    print("Cleaning up old Whisper models...")
    print("=" * 50)
    
    model_cache_dir = Path("model_cache")
    
    if not model_cache_dir.exists():
        print("No model_cache directory found. Nothing to clean.")
        return
    
    # Models to remove (old distil-whisper models)
    models_to_remove = [
        "models--Systran--faster-distil-whisper-large-v3",
        "models--distil-whisper--distil-large-v3",
        "models--Systran--faster-whisper-large-v3",  # Also remove large model
    ]
    
    # Models to keep (if they exist)
    models_to_keep = [
        "models--Systran--faster-whisper-base",
        # medium.en will be downloaded automatically when needed
    ]
    
    total_freed = 0
    
    for model_dir in models_to_remove:
        model_path = model_cache_dir / model_dir
        
        if model_path.exists():
            size_mb = get_directory_size(model_path)
            print(f"Removing {model_dir} ({size_mb:.1f} MB)...")
            
            try:
                shutil.rmtree(model_path)
                print(f"✓ Removed {model_dir}")
                total_freed += size_mb
            except Exception as e:
                print(f"✗ Failed to remove {model_dir}: {e}")
        else:
            print(f"⚠️  {model_dir} not found (already removed?)")
    
    print(f"\n✓ Cleanup completed!")
    print(f"✓ Total space freed: {total_freed:.1f} MB")
    
    # Show remaining models
    remaining_models = []
    for item in model_cache_dir.iterdir():
        if item.is_dir() and item.name.startswith("models--"):
            size_mb = get_directory_size(item)
            remaining_models.append((item.name, size_mb))
    
    if remaining_models:
        print(f"\nRemaining models:")
        for model_name, size_mb in remaining_models:
            print(f"  - {model_name} ({size_mb:.1f} MB)")
    else:
        print(f"\nNo models remaining in cache.")
        print(f"medium.en will be downloaded automatically on first use.")
    
    print(f"\nNote: The new medium.en model will be downloaded automatically")
    print(f"when you first run the transcription. This is normal and expected.")

if __name__ == "__main__":
    main()
