#!/usr/bin/env python3
"""
Installation script for Whisper Medium.en optimized for GTX 1650
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✓ Success")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def main():
    print("Installing Whisper Medium.en for GTX 1650...")
    print("=" * 50)
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 9) or python_version >= (3, 12):
        print("⚠️  Warning: Python 3.9-3.11 is recommended for best compatibility")
    
    # Install PyTorch with CUDA support
    print("\n1. Installing PyTorch with CUDA 11.8 support...")
    torch_command = "pip install --upgrade torch --index-url https://download.pytorch.org/whl/cu118"
    if not run_command(torch_command, "Installing PyTorch CUDA"):
        print("Failed to install PyTorch. Please install manually.")
        return False
    
    # Install core dependencies
    print("\n2. Installing core dependencies...")
    core_deps = [
        "faster-whisper==1.0.3",
        "soundfile==0.12.1", 
        "numpy==1.26.4",
        "sounddevice",
        "scipy",
        "noisereduce"
    ]
    
    for dep in core_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"Failed to install {dep}")
            return False
    
    # Optional: Install grammar correction tools
    print("\n3. Installing optional grammar correction tools...")
    
    # Ask user about LanguageTool
    response = input("Install LanguageTool for offline grammar correction? (y/n): ").lower()
    if response in ['y', 'yes']:
        if not run_command("pip install language-tool-python==2.7.1", "Installing LanguageTool"):
            print("Failed to install LanguageTool, but continuing...")
    
    # Ask user about OpenAI
    response = input("Install OpenAI client for API-based grammar correction? (y/n): ").lower()
    if response in ['y', 'yes']:
        if not run_command("pip install openai==1.44.0", "Installing OpenAI client"):
            print("Failed to install OpenAI client, but continuing...")
    
    print("\n4. Testing installation...")
    
    # Test imports
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__} installed")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ CUDA device: {torch.cuda.get_device_name(0)}")
    except ImportError:
        print("✗ PyTorch import failed")
        return False
    
    try:
        from faster_whisper import WhisperModel
        print("✓ faster-whisper imported successfully")
    except ImportError:
        print("✗ faster-whisper import failed")
        return False
    
    try:
        import soundfile
        import sounddevice
        print("✓ Audio libraries imported successfully")
    except ImportError:
        print("✗ Audio libraries import failed")
        return False
    
    print("\n" + "=" * 50)
    print("✓ Installation completed successfully!")
    print("\nNext steps:")
    print("1. Test recording: python quick_record.py")
    print("2. Test transcription: python stt_pipeline.py")
    print("3. Test full pipeline: python chatbot_pipeline.py")
    print("\nFor best results on GTX 1650:")
    print("- Use medium.en model (default)")
    print("- Use int8_float16 compute type (default)")
    print("- Keep audio clips under 30 seconds for optimal memory usage")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
