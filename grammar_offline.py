# grammar_offline.py
# Requires: pip install language-tool-python
import language_tool_python

# Initialize the grammar checker once
tool = language_tool_python.LanguageTool('en-US')

def grammar_correct_offline(text: str) -> str:
    """
    Apply offline grammar correction using LanguageTool.
    
    Args:
        text: Input text to correct
        
    Returns:
        str: Grammar-corrected text
    """
    if not text or not text.strip():
        return text
        
    try:
        matches = tool.check(text)
        corrected = language_tool_python.utils.correct(text, matches)
        return corrected
    except Exception as e:
        print(f"Grammar correction failed: {e}")
        return text

if __name__ == "__main__":
    raw = "He go to supermarket yesterday and buy two apple."
    corrected = grammar_correct_offline(raw)
    print("Original:", raw)
    print("Corrected:", corrected)
