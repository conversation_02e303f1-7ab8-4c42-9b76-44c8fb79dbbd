#!/usr/bin/env python3
"""
Test script for Whisper Medium.en setup on GTX 1650
"""

import os
import sys
import time
import numpy as np

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
            memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✓ GPU Memory: {memory_gb:.1f} GB")
        else:
            print("⚠️  CUDA not available, will use CPU")
            
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
    
    try:
        from faster_whisper import WhisperModel
        print("✓ faster-whisper")
    except ImportError as e:
        print(f"✗ faster-whisper import failed: {e}")
        return False
    
    try:
        import soundfile as sf
        import sounddevice as sd
        print("✓ Audio libraries (soundfile, sounddevice)")
    except ImportError as e:
        print(f"✗ Audio libraries import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    return True

def test_model_loading():
    """Test loading the Whisper medium.en model"""
    print("\nTesting model loading...")
    
    try:
        from faster_whisper import WhisperModel
        import torch
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        compute_type = "int8_float16" if device == "cuda" else "int8"
        
        print(f"Loading medium.en model on {device} with {compute_type}...")
        start_time = time.time()
        
        model = WhisperModel(
            "medium.en",
            device=device,
            compute_type=compute_type,
            num_workers=1,
            cpu_threads=0
        )
        
        load_time = time.time() - start_time
        print(f"✓ Model loaded successfully in {load_time:.1f} seconds")
        
        return model
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return None

def test_transcription(model):
    """Test transcription with a simple audio sample"""
    print("\nTesting transcription...")
    
    try:
        # Generate a simple test audio (silence)
        sample_rate = 16000
        duration = 2  # 2 seconds
        audio = np.zeros(sample_rate * duration, dtype=np.float32)
        
        print("Transcribing test audio (silence)...")
        start_time = time.time()
        
        segments, info = model.transcribe(
            audio,
            language="en",
            beam_size=5,
            temperature=[0.0, 0.2, 0.4],
            vad_filter=True,
            word_timestamps=False
        )
        
        # Convert segments to list
        segment_list = list(segments)
        transcription_time = time.time() - start_time
        
        print(f"✓ Transcription completed in {transcription_time:.1f} seconds")
        print(f"✓ Detected language: {info.language}")
        print(f"✓ Language probability: {info.language_probability:.2f}")
        print(f"✓ Duration: {info.duration:.1f} seconds")
        print(f"✓ Segments: {len(segment_list)}")
        
        if segment_list:
            print("✓ Transcription result: (silence detected as expected)")
        else:
            print("✓ No speech detected in silence (correct behavior)")
        
        return True
        
    except Exception as e:
        print(f"✗ Transcription test failed: {e}")
        return False

def test_audio_devices():
    """Test audio device availability"""
    print("\nTesting audio devices...")
    
    try:
        import sounddevice as sd
        
        devices = sd.query_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
        
        print(f"✓ Found {len(input_devices)} input devices:")
        for i, device in enumerate(input_devices[:3]):  # Show first 3
            print(f"  {i+1}. {device['name']} ({device['default_samplerate']} Hz)")
        
        if len(input_devices) > 3:
            print(f"  ... and {len(input_devices) - 3} more")
        
        return len(input_devices) > 0
        
    except Exception as e:
        print(f"✗ Audio device test failed: {e}")
        return False

def test_grammar_correction():
    """Test grammar correction modules"""
    print("\nTesting grammar correction...")
    
    # Test simple grammar correction
    try:
        from grammar_api import grammar_correct_simple_api
        test_text = "he go to store yesterday"
        corrected = grammar_correct_simple_api(test_text)
        print(f"✓ Simple grammar correction: '{test_text}' -> '{corrected}'")
    except Exception as e:
        print(f"⚠️  Simple grammar correction failed: {e}")
    
    # Test LanguageTool if available
    try:
        from grammar_offline import grammar_correct_offline
        test_text = "he go to store yesterday"
        corrected = grammar_correct_offline(test_text)
        print(f"✓ LanguageTool correction: '{test_text}' -> '{corrected}'")
    except ImportError:
        print("⚠️  LanguageTool not installed (optional)")
    except Exception as e:
        print(f"⚠️  LanguageTool test failed: {e}")
    
    # Test OpenAI if available and configured
    try:
        import openai
        if os.getenv("OPENAI_API_KEY"):
            print("✓ OpenAI client available and API key set")
        else:
            print("⚠️  OpenAI client available but no API key set")
    except ImportError:
        print("⚠️  OpenAI client not installed (optional)")

def main():
    print("Whisper Medium.en Test Suite for GTX 1650")
    print("=" * 50)
    
    # Test 1: Imports
    if not test_imports():
        print("\n✗ Import tests failed. Please check your installation.")
        return False
    
    # Test 2: Model loading
    model = test_model_loading()
    if not model:
        print("\n✗ Model loading failed. Please check your installation.")
        return False
    
    # Test 3: Transcription
    if not test_transcription(model):
        print("\n✗ Transcription test failed.")
        return False
    
    # Test 4: Audio devices
    if not test_audio_devices():
        print("\n⚠️  No audio input devices found. Recording may not work.")
    
    # Test 5: Grammar correction
    test_grammar_correction()
    
    print("\n" + "=" * 50)
    print("✓ All core tests passed!")
    print("\nYour Whisper Medium.en setup is ready for GTX 1650!")
    print("\nRecommended usage:")
    print("- Record audio: python quick_record.py")
    print("- Transcribe file: python stt_pipeline.py <audio_file>")
    print("- Full pipeline: python chatbot_pipeline.py <audio_file>")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
