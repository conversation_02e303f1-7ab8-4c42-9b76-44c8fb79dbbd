# grammar_api.py
# Requires: pip install openai
import openai
import os
from typing import Optional

# Set your OpenAI API key
openai.api_key = os.getenv("OPENAI_API_KEY")

def grammar_correct_api(text: str, model: str = "gpt-3.5-turbo") -> str:
    """
    Apply grammar correction using OpenAI API.
    
    Args:
        text: Input text to correct
        model: OpenAI model to use (gpt-3.5-turbo or gpt-4)
        
    Returns:
        str: Grammar-corrected text
    """
    if not text or not text.strip():
        return text
        
    if not openai.api_key:
        print("Warning: OpenAI API key not set. Returning original text.")
        return text
        
    try:
        response = openai.ChatCompletion.create(
            model=model,
            messages=[
                {
                    "role": "system",
                    "content": "You are a grammar correction assistant. Fix grammar, spelling, and punctuation errors while preserving the original meaning and tone. Return only the corrected text without explanations."
                },
                {
                    "role": "user",
                    "content": f"Please correct the grammar in this text: {text}"
                }
            ],
            max_tokens=len(text.split()) * 2,  # Allow for expansion
            temperature=0.1,  # Low temperature for consistent corrections
        )
        
        corrected = response.choices[0].message.content.strip()
        return corrected
        
    except Exception as e:
        print(f"API grammar correction failed: {e}")
        return text

def grammar_correct_simple_api(text: str) -> str:
    """
    Simple grammar correction using a lightweight approach.
    Fallback when full API is not available.
    """
    if not text or not text.strip():
        return text
        
    # Basic corrections that can be done without API
    corrections = {
        " i ": " I ",
        " im ": " I'm ",
        " dont ": " don't ",
        " cant ": " can't ",
        " wont ": " won't ",
        " youre ": " you're ",
        " theyre ": " they're ",
        " were ": " we're ",
    }
    
    corrected = text
    for wrong, right in corrections.items():
        corrected = corrected.replace(wrong, right)
    
    # Capitalize first letter
    if corrected and corrected[0].islower():
        corrected = corrected[0].upper() + corrected[1:]
    
    return corrected

if __name__ == "__main__":
    raw = "he go to supermarket yesterday and buy two apple"
    
    # Try API correction first
    corrected_api = grammar_correct_api(raw)
    print("Original:", raw)
    print("API Corrected:", corrected_api)
    
    # Try simple correction as fallback
    corrected_simple = grammar_correct_simple_api(raw)
    print("Simple Corrected:", corrected_simple)
