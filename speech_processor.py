import sounddevice as sd
import numpy as np
import torch
import time
import scipy.signal
import noisereduce as nr
import logging
import os
from pathlib import Path
import librosa

# Try to import faster-whisper first, fallback to ctranslate2 approach
try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
    print("Using faster-whisper for speech recognition")
except ImportError:
    FASTER_WHISPER_AVAILABLE = False
    print("faster-whisper not available, using alternative approach")

# Fallback imports for CTranslate2 approach
if not FASTER_WHISPER_AVAILABLE:
    try:
        import ctranslate2
        from transformers import AutoProcessor, AutoTokenizer
        from huggingface_hub import snapshot_download
        CTRANSLATE2_AVAILABLE = True
    except ImportError:
        CTRANSLATE2_AVAILABLE = False

# Language processing imports
try:
    from spellchecker import Spell<PERSON>hecker
    from textblob import TextBlob
    LANGUAGE_TOOLS_AVAILABLE = True
except ImportError:
    LANGUAGE_TOOLS_AVAILABLE = False
    print("Language processing tools not available, skipping post-processing")

# Configure logging for CTranslate2
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SpeechProcessor:
    """
    Advanced Speech-to-Text processor using faster-whisper with Whisper Medium.en
    Optimized for NVIDIA GTX 1650 GPU (4GB VRAM) with enhanced accuracy and speed
    """

    def __init__(self):
        try:
            # Set environment variables for Windows compatibility
            os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'
            os.environ['HF_HUB_DISABLE_SYMLINKS'] = '1'

            # Device configuration optimized for GTX 1650 (4GB VRAM)
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            # Use int8_float16 for GTX 1650 to fit 4GB VRAM while maintaining accuracy
            self.compute_type = "int8_float16" if self.device == "cuda" else "int8"

            if FASTER_WHISPER_AVAILABLE:
                # Use faster-whisper with medium.en model optimized for GTX 1650
                # medium.en provides highest accuracy that fits in 4GB VRAM with int8_float16
                self.model_name = os.getenv("WHISPER_MODEL", "medium.en")
                print(f"Initializing faster-whisper with {self.model_name} on {self.device} using {self.compute_type} precision...")

                # Use local cache directory to avoid permission issues
                local_cache_dir = Path("./model_cache")
                local_cache_dir.mkdir(exist_ok=True)

                self.model = WhisperModel(
                    self.model_name,
                    device=self.device,
                    compute_type=self.compute_type,
                    device_index=0 if self.device == "cuda" else None,
                    num_workers=1,  # Keep memory usage predictable for GTX 1650
                    cpu_threads=0,  # Let runtime pick optimal threads
                    download_root=str(local_cache_dir),
                    local_files_only=False,
                )
                self.use_faster_whisper = True

                # Set processor and tokenizer to None for faster-whisper
                self.processor = None
                self.tokenizer = None

            else:
                # Fallback to CTranslate2 approach (if available)
                if not CTRANSLATE2_AVAILABLE:
                    raise ImportError("Neither faster-whisper nor ctranslate2 is available")

                # Use medium.en model for fallback as well
                self.model_name = "openai/whisper-medium.en"
                print(f"Initializing CTranslate2 with {self.model_name} on {self.device} using {self.compute_type} precision...")

                # Download and cache the model
                self.model_path = self._download_model()

                # Initialize CTranslate2 model with optimized settings for GTX 1650
                self.model = ctranslate2.models.Whisper(
                    self.model_path,
                    device=self.device,
                    compute_type=self.compute_type,
                    device_index=0,  # Use first GPU
                    inter_threads=2,  # Optimized for GTX 1650
                    intra_threads=4,  # Balanced threading
                )

                # Initialize processor and tokenizer for feature extraction
                self.processor = AutoProcessor.from_pretrained(self.model_name)
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.use_faster_whisper = False

            # Enhanced audio configuration optimized for Whisper medium.en
            self.sample_rate = 16000  # Whisper's expected sample rate
            self.chunk_duration = 30  # Maximum chunk duration for Whisper

            # Fine-tuned transcription parameters for maximum accuracy with medium.en
            self.transcription_params = {
                'language': 'en',
                'beam_size': 8,        # Increased for maximum accuracy
                'best_of': 8,          # More candidates for best results
                'patience': 1.5,       # Higher patience for thorough search
                'length_penalty': 1.0,
                'repetition_penalty': 1.1,  # Stronger repetition penalty
                'no_repeat_ngram_size': 4,   # Prevent longer repetitions
                'temperature': [0.0, 0.1, 0.2, 0.3],  # More temperature steps for accuracy
                'compression_ratio_threshold': 2.2,  # More sensitive to repetitive text
                'log_prob_threshold': -0.8,  # Higher confidence threshold
                'no_speech_threshold': 0.5,  # Lower threshold for better speech detection
                'condition_on_previous_text': True,
                'initial_prompt': "This is clear English speech with proper pronunciation and grammar.",
                'suppress_blank': True,
                'suppress_tokens': [-1],
                'word_timestamps': False,  # Disable for better speed in real-time
                'vad_filter': True,
                'vad_parameters': {
                    'min_silence_duration_ms': 300,  # Faster turn-taking for real-time
                    'speech_pad_ms': 50,             # Minimal padding for responsiveness
                    'threshold': 0.15                # More sensitive VAD
                }
            }

            print(f"Whisper Medium.en model loaded successfully on {self.device}")
            print(f"Model: {self.model_name} with {self.compute_type} precision")
            print(f"Optimized for GTX 1650 (4GB VRAM)")
            print(f"Model parameters: {self.transcription_params}")

            # Initialize language processing tools
            self.spell_checker = None
            if LANGUAGE_TOOLS_AVAILABLE:
                try:
                    self.spell_checker = SpellChecker()
                    print("Spell checker initialized")
                except Exception as e:
                    print(f"Failed to initialize spell checker: {e}")

            # Test audio device
            self._test_audio_device()

            # Initialize speech-optimized parameters
            self._initialize_speech_optimization()

        except Exception as e:
            print(f"Error initializing SpeechProcessor: {e}")
            raise

    def _download_model(self):
        """Download and cache the CTranslate2 model"""
        try:
            # Set environment variable to disable symlinks
            os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'

            # Use a local cache directory to avoid permission issues
            local_cache_dir = Path("./model_cache")
            local_cache_dir.mkdir(exist_ok=True)

            print("Downloading Systran/faster-distil-whisper-large-v3 model...")

            # Download the model using huggingface_hub with local cache
            downloaded_path = snapshot_download(
                repo_id=self.model_name,
                cache_dir=str(local_cache_dir),
                local_files_only=False,
                revision="main"
            )

            print(f"Model downloaded to {downloaded_path}")

            # Verify the model.bin file exists
            model_bin_path = Path(downloaded_path) / "model.bin"
            if not model_bin_path.exists():
                raise FileNotFoundError(f"model.bin not found in {downloaded_path}")

            return downloaded_path

        except Exception as e:
            logger.error(f"Failed to download model: {e}")
            raise

    def _initialize_speech_optimization(self):
        """Initialize speech recognition optimization parameters"""
        try:
            # Speech-specific optimization settings for medium.en
            self.speech_params = {
                'min_speech_duration': 0.3,    # Minimum speech duration in seconds
                'max_speech_duration': 30.0,   # Maximum speech duration in seconds
                'confidence_threshold': 0.3,   # Minimum confidence for accepting results
                'silence_threshold': 0.01,     # RMS threshold for silence detection
                'speech_enhancement': True,    # Enable speech enhancement
                'real_time_factor': 1.0,      # Real-time processing factor
            }

            # Adaptive processing parameters
            self.adaptive_params = {
                'min_beam_size': 3,
                'max_beam_size': 10,
                'beam_adjustment_factor': 1.5,
                'quality_threshold': 0.5,
                'speed_threshold': 2.0,  # seconds per second of audio
            }

            print("Speech optimization parameters initialized")

        except Exception as e:
            print(f"Speech optimization initialization failed: {e}")
        
    def _test_audio_device(self):
        """Test if audio device is available"""
        try:
            # Get available audio devices
            devices = sd.query_devices()
            if not devices:
                print("Warning: No audio devices found.")
                return
            print(f"Available audio devices: {len(devices)}")
            
            # Test a very short recording
            sd.rec(int(0.1 * self.sample_rate), 
                   samplerate=self.sample_rate, 
                   channels=1, 
                   dtype='float32')
            sd.wait()
            print("Audio device test successful")
            
        except Exception as e:
            print(f"Warning: Audio device test failed: {e}")

    def _detect_voice_activity(self, audio, sample_rate=16000):
        """Detect voice activity in audio using speech-optimized detection"""
        try:
            return self._enhanced_speech_vad(audio)
        except Exception as e:
            print(f"Voice activity detection failed: {e}")
            return True  # Assume voice is present if detection fails

    def _enhanced_speech_vad(self, audio):
        """Enhanced speech-optimized voice activity detection"""
        try:
            # Speech-optimized frame parameters
            frame_size = 400   # 25ms at 16kHz - optimal for speech analysis
            hop_size = 160     # 10ms at 16kHz - good time resolution

            # Pre-allocate arrays for efficiency
            num_frames = (len(audio) - frame_size) // hop_size + 1
            energies = np.zeros(num_frames)
            spectral_centroids = np.zeros(num_frames)
            zero_crossing_rates = np.zeros(num_frames)
            speech_band_energies = np.zeros(num_frames)  # Energy in speech frequencies

            # Vectorized processing for speed
            for i in range(num_frames):
                start_idx = i * hop_size
                end_idx = start_idx + frame_size
                frame = audio[start_idx:end_idx]

                # Energy calculation
                energies[i] = np.sum(frame ** 2)

                # FFT for spectral analysis
                fft = np.fft.fft(frame)
                magnitude = np.abs(fft[:len(fft)//2])
                freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)[:len(fft)//2]

                # Spectral centroid (focus on speech frequencies)
                total_magnitude = np.sum(magnitude)
                if total_magnitude > 0:
                    spectral_centroids[i] = np.sum(freqs * magnitude) / total_magnitude

                # Zero crossing rate (important for speech detection)
                zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
                zero_crossing_rates[i] = zero_crossings / len(frame)

                # Speech band energy (300Hz - 3400Hz)
                speech_mask = (freqs >= 300) & (freqs <= 3400)
                speech_band_energies[i] = np.sum(magnitude[speech_mask]) if np.any(speech_mask) else 0
            
            # Speech-optimized statistical analysis
            energy_threshold = np.mean(energies) + 0.4 * np.std(energies)
            energy_voice_frames = np.sum(energies > energy_threshold)
            energy_ratio = energy_voice_frames / len(energies)

            # Speech frequency analysis (300Hz - 3400Hz is primary speech band)
            speech_centroid_frames = np.sum((spectral_centroids > 300) & (spectral_centroids < 3400))
            centroid_ratio = speech_centroid_frames / len(spectral_centroids)

            # Speech-optimized ZCR analysis
            speech_zcr_frames = np.sum((zero_crossing_rates > 0.01) & (zero_crossing_rates < 0.3))
            zcr_ratio = speech_zcr_frames / len(zero_crossing_rates)

            # Speech band energy analysis
            speech_energy_threshold = np.mean(speech_band_energies) + 0.3 * np.std(speech_band_energies)
            speech_energy_frames = np.sum(speech_band_energies > speech_energy_threshold)
            speech_energy_ratio = speech_energy_frames / len(speech_band_energies)

            # Speech-optimized weighted decision
            voice_score = (
                0.35 * energy_ratio +         # Overall energy
                0.25 * speech_energy_ratio +  # Speech band energy - very important
                0.25 * centroid_ratio +       # Spectral centroid in speech range
                0.15 * zcr_ratio             # Zero crossing rate
            )

            print(f"Speech-VAD - Energy: {energy_ratio:.2f}, Speech-band: {speech_energy_ratio:.2f}, "
                  f"Centroid: {centroid_ratio:.2f}, ZCR: {zcr_ratio:.2f}, Score: {voice_score:.2f}")

            # Adaptive threshold for speech detection
            base_threshold = 0.25
            if np.std(energies) > np.mean(energies) * 0.6:  # High variance indicates speech
                base_threshold = 0.2

            return voice_score > base_threshold

        except Exception as e:
            print(f"Speech VAD failed: {e}")
            return True

    def _preprocess_audio(self, audio):
        """Optimized audio preprocessing for maximum medium.en accuracy"""
        try:
            # Ensure audio is float32
            if audio.dtype != np.float32:
                audio = audio.astype(np.float32)

            # Remove DC offset
            audio = (audio - np.mean(audio)).astype(np.float32)

            # Optimized noise reduction for speech clarity
            try:
                audio = nr.reduce_noise(
                    y=audio,
                    sr=self.sample_rate,
                    stationary=True,
                    prop_decrease=0.8,   # Balanced noise reduction
                    time_constant_s=2.0, # Stable adaptation
                    freq_mask_smooth_hz=500,  # Speech-optimized frequency resolution
                    time_mask_smooth_ms=50    # Speech-optimized time resolution
                ).astype(np.float32)
                print("Applied speech-optimized noise reduction")
            except Exception as e:
                print(f"Noise reduction failed: {e}")

            # Intelligent normalization for speech
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                rms = np.sqrt(np.mean(np.square(audio)))

                # Speech-optimized normalization levels
                if rms > 0.08:      # Very strong signal
                    target_level = 0.7
                elif rms > 0.04:    # Strong signal
                    target_level = 0.8
                elif rms > 0.015:   # Normal signal
                    target_level = 0.85
                else:               # Weak signal
                    target_level = 0.9

                audio = (audio / max_val * target_level).astype(np.float32)
                print(f"Speech normalization: RMS {rms:.4f} -> target {target_level}")

            # Speech-optimized frequency filtering
            try:
                nyquist = self.sample_rate / 2

                # High-pass filter optimized for speech (remove low-frequency noise)
                low_cutoff = 80  # Hz - removes most environmental noise
                high = low_cutoff / nyquist
                b, a = scipy.signal.butter(4, high, btype='high')
                audio = scipy.signal.filtfilt(b, a, audio).astype(np.float32)

                # Low-pass filter optimized for speech intelligibility
                high_cutoff = 8000  # Hz - preserves speech harmonics
                low = high_cutoff / nyquist
                b, a = scipy.signal.butter(4, low, btype='low')
                audio = scipy.signal.filtfilt(b, a, audio).astype(np.float32)

                print("Applied speech-optimized filtering (80Hz-8kHz)")

            except Exception as e:
                print(f"Filtering failed: {e}")

            # Speech-optimized dynamic range compression
            try:
                # Gentle compression for natural speech dynamics
                threshold = 0.6
                ratio = 2.5

                # Apply soft compression
                compressed = np.where(
                    np.abs(audio) > threshold,
                    np.sign(audio) * (threshold + (np.abs(audio) - threshold) / ratio),
                    audio
                ).astype(np.float32)

                # Preserve natural speech dynamics
                audio = (0.7 * compressed + 0.3 * audio).astype(np.float32)
                print("Applied speech-optimized compression")

            except Exception as e:
                print(f"Compression failed: {e}")

            # Final speech-optimized normalization
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                # Leave some headroom for natural speech dynamics
                audio = (audio / max_val * 0.9).astype(np.float32)

            return audio.astype(np.float32)

        except Exception as e:
            print(f"Audio preprocessing failed: {e}")
            return audio

    def _get_optimal_device(self):
        """Get the optimal audio input device for speech recognition"""
        try:
            devices = sd.query_devices()
            input_devices = [d for d in devices if d['max_input_channels'] > 0]

            if not input_devices:
                return None

            # Speech-optimized device selection
            best_device = None
            best_score = 0

            for i, device in enumerate(input_devices):
                score = 0

                # Prefer devices with good sample rates for speech
                sr = device['default_samplerate']
                if sr >= 16000:
                    if sr in [16000, 22050, 44100, 48000]:  # Common good rates
                        score += 3
                    elif sr >= 44100:
                        score += 2
                    else:
                        score += 1

                # Prefer lower latency for real-time processing
                if device['default_low_input_latency'] < 0.05:
                    score += 2
                elif device['default_low_input_latency'] < 0.1:
                    score += 1

                # Prefer default device
                if i == sd.default.device[0]:
                    score += 1

                if score > best_score:
                    best_score = score
                    best_device = i

            return best_device

        except Exception as e:
            print(f"Device selection failed: {e}")
            return None

    def record_audio(self, duration=5, stop_callback=None):
        """Record audio optimized for speech recognition with real-time monitoring"""
        print(f"Recording for {duration} seconds...")
        try:
            if duration <= 0 or duration > 30:  # Allow longer recordings
                duration = 5

            # Get optimal recording device
            device = self._get_optimal_device()
            if device is not None:
                print(f"Using audio device: {sd.query_devices(device)['name']}")

            # Speech-optimized recording parameters
            recording_params = {
                'frames': int(duration * self.sample_rate),
                'samplerate': self.sample_rate,
                'channels': 1,
                'dtype': 'float32',
                'device': device,
                'blocking': False,
                'latency': 'low'
            }

            # Start recording
            audio = sd.rec(**recording_params)

            # Real-time monitoring for speech quality
            print("Recording... (Speech-optimized monitoring)")
            monitor_interval = 0.1  # More frequent checks for better responsiveness
            checks = int(duration / monitor_interval)

            for i in range(checks):
                time.sleep(monitor_interval)

                # Check stop callback if provided
                if stop_callback and callable(stop_callback):
                    try:
                        if stop_callback():
                            print("Recording stopped by callback")
                            sd.stop()
                            # Get partial audio data
                            current_samples = int((i + 1) * monitor_interval * self.sample_rate)
                            if current_samples > 0:
                                partial_audio = audio[:current_samples].flatten()
                                if len(partial_audio) > self.sample_rate * 0.5:  # At least 0.5 seconds
                                    return self._preprocess_audio(partial_audio)
                            return None
                    except Exception as e:
                        print(f"Error in stop callback: {e}")

                current_samples = int((i + 1) * monitor_interval * self.sample_rate)
                
                if current_samples < len(audio):
                    current_audio = audio[:current_samples].flatten()
                    if len(current_audio) > 0:
                        # Enhanced monitoring metrics
                        recent_audio = current_audio[-int(monitor_interval * self.sample_rate):]
                        
                        current_level = np.max(np.abs(recent_audio))
                        rms_level = np.sqrt(np.mean(np.square(recent_audio)))
                        level_db = 20 * np.log10(max(current_level, 1e-10))
                        
                        # Signal-to-noise ratio estimate
                        snr_estimate = 20 * np.log10(max(rms_level / (np.std(recent_audio) + 1e-10), 1e-10))
                        
                        print(f"  Check {i+1}/{checks}: {level_db:.1f} dB, SNR: {snr_estimate:.1f} dB")

                        if i == 0 and current_level < 0.005:
                            print("  Warning: Very low audio level detected!")

            # Wait for recording to complete
            sd.wait()

            if audio is None or audio.size == 0:
                raise ValueError("No audio data received from recording device.")

            audio_flat = audio.flatten()

            # Enhanced audio validation
            rms = np.sqrt(np.mean(np.square(audio_flat)))
            max_amplitude = np.max(np.abs(audio_flat))
            dynamic_range = max_amplitude / (rms + 1e-10)

            print(f"Raw audio - Max: {max_amplitude:.4f}, RMS: {rms:.4f}, DR: {dynamic_range:.1f}")

            # Speech quality validation
            if max_amplitude < 0.01:
                raise ValueError("Audio level too low for speech recognition. Please speak louder.")

            # Enhanced voice activity detection
            has_voice = self._detect_voice_activity(audio_flat, self.sample_rate)
            if not has_voice:
                raise ValueError("No clear speech detected. Please speak more clearly.")

            # Apply speech-optimized preprocessing
            processed_audio = self._preprocess_audio(audio_flat)

            # Final validation
            final_rms = np.sqrt(np.mean(np.square(processed_audio)))
            final_max = np.max(np.abs(processed_audio))
            final_dr = final_max / (final_rms + 1e-10)

            print(f"Processed audio - Max: {final_max:.4f}, RMS: {final_rms:.4f}, DR: {final_dr:.1f}")
            print(f"Speech-ready audio: {len(processed_audio)} samples ({len(processed_audio)/self.sample_rate:.1f}s)")

            return processed_audio

        except Exception as e:
            print(f"Error in record_audio: {e}")
            raise RuntimeError(f"Failed to record audio: {str(e)}")

    def stop_recording(self):
        """Stop any active recording"""
        try:
            sd.stop()
            print("Recording stopped")
        except Exception as e:
            print(f"Error stopping recording: {e}")

    def _spell_check_text(self, text):
        """Apply spell checking with CTC-specific optimizations"""
        if not LANGUAGE_TOOLS_AVAILABLE or not self.spell_checker or not text:
            return text

        try:
            # Tokenize more intelligently for CTC results
            import re
            
            # Split on word boundaries while preserving punctuation
            tokens = re.findall(r'\b\w+\b|[^\w\s]', text)
            corrected_tokens = []

            for token in tokens:
                if token.isalpha():
                    # Check if word is misspelled
                    if token.lower() not in self.spell_checker:
                        candidates = self.spell_checker.candidates(token.lower())
                        if candidates:
                            # Use the most likely candidate
                            correction = list(candidates)[0]
                            
                            # Preserve original capitalization
                            if token.isupper():
                                correction = correction.upper()
                            elif token.istitle():
                                correction = correction.capitalize()
                            
                            corrected_tokens.append(correction)
                            print(f"CTC spell correction: '{token}' -> '{correction}'")
                        else:
                            corrected_tokens.append(token)
                    else:
                        corrected_tokens.append(token)
                else:
                    corrected_tokens.append(token)

            # Reconstruct text with proper spacing
            result = ""
            for i, token in enumerate(corrected_tokens):
                if i > 0 and token.isalpha() and corrected_tokens[i-1].isalpha():
                    result += " "
                result += token

            return result

        except Exception as e:
            print(f"CTC spell checking failed: {e}")
            return text

    def _grammar_check_text(self, text):
        """Apply grammar checking optimized for CTC output"""
        if not LANGUAGE_TOOLS_AVAILABLE or not text:
            return text

        try:
            # More conservative grammar checking for CTC
            blob = TextBlob(text)
            corrected = blob.correct()
            corrected_text = str(corrected)

            # Only apply if the change is significant and improves readability
            if corrected_text != text:
                # Check if the correction makes sense
                original_words = text.split()
                corrected_words = corrected_text.split()
                
                # Only apply if word count is similar (avoid over-correction)
                if abs(len(original_words) - len(corrected_words)) <= 2:
                    print(f"CTC grammar correction: '{text}' -> '{corrected_text}'")
                    return corrected_text

            return text

        except Exception as e:
            print(f"CTC grammar checking failed: {e}")
            return text

    def _clean_transcription(self, text):
        """Enhanced cleaning and post-processing for better transcription accuracy"""
        if not text:
            return text

        # Enhanced CTC-specific artifacts removal
        ctc_artifacts = [
            "Thank you.", "Thanks for watching.", "Thank you for watching.",
            "Bye.", "Goodbye.", "See you next time.", "See you later.",
            "Subscribe.", "Like and subscribe.", "Don't forget to subscribe.",
            "♪", "♫", "[Music]", "[Applause]", "[Laughter]", "[BLANK_AUDIO]",
            "Hmm.", "Um.", "Uh.", "Er.", "Ah.", "Oh.", "Well.",
            "Mmm.", "Mm-hmm.", "Uh-huh.", "Okay.", "OK.", "Yeah.", "Yes.",
            "you", "I", "the", "and", "a", "to"  # Common single-word hallucinations
        ]

        # Additional patterns to remove
        import re

        # Clean up text
        cleaned = text.strip()

        # Remove timestamps and markers
        cleaned = re.sub(r'\[\d+:\d+\]', '', cleaned)
        cleaned = re.sub(r'\(\d+:\d+\)', '', cleaned)
        cleaned = re.sub(r'<[^>]*>', '', cleaned)

        # Remove repeated characters (common CTC artifact)
        cleaned = re.sub(r'(.)\1{3,}', r'\1\1', cleaned)

        # Remove artifacts (case-insensitive and more thorough)
        for artifact in ctc_artifacts:
            # Remove exact matches at beginning/end
            pattern_start = re.compile(r'^' + re.escape(artifact.lower()) + r'\s*', re.IGNORECASE)
            pattern_end = re.compile(r'\s*' + re.escape(artifact.lower()) + r'$', re.IGNORECASE)

            cleaned = pattern_start.sub('', cleaned)
            cleaned = pattern_end.sub('', cleaned)

        # Remove standalone single characters that are likely artifacts
        words = cleaned.split()
        filtered_words = []
        for word in words:
            # Keep single characters only if they're meaningful (I, a, etc.)
            if len(word) == 1 and word.lower() not in ['i', 'a']:
                continue
            filtered_words.append(word)

        cleaned = ' '.join(filtered_words)

        # Remove extra whitespace and normalize
        cleaned = ' '.join(cleaned.split())

        # Remove leading/trailing punctuation artifacts
        cleaned = cleaned.strip('.,!?;:')

        if not cleaned or len(cleaned) < 2:
            return cleaned

        # Apply enhanced post-processing
        cleaned = self._spell_check_text(cleaned)
        cleaned = self._grammar_check_text(cleaned)

        # Final capitalization fix
        if cleaned and len(cleaned) > 0:
            cleaned = cleaned[0].upper() + cleaned[1:] if len(cleaned) > 1 else cleaned.upper()

        return cleaned

    def speech_to_text(self, audio):
        """
        Enhanced speech-to-text with faster-whisper or CTranslate2 fallback
        Optimized for GTX 1650 GPU with improved accuracy
        """
        if audio is None or len(audio) == 0:
            raise ValueError("No audio data provided for transcription.")

        try:
            # Ensure audio is properly formatted
            if audio.dtype != np.float32:
                audio = audio.astype(np.float32)

            # Enhanced audio validation
            max_amp = np.max(np.abs(audio))
            rms = np.sqrt(np.mean(np.square(audio)))
            snr_estimate = 20 * np.log10(max(rms / (np.std(audio) + 1e-10), 1e-10))

            print(f"Audio input validation - Max: {max_amp:.4f}, RMS: {rms:.4f}, SNR: {snr_estimate:.1f} dB")

            if max_amp < 0.005:
                return "The audio was too quiet for processing. Please speak louder."

            # Ensure audio is in the correct range
            if max_amp > 1.0:
                audio = audio / max_amp * 0.95

            if self.use_faster_whisper:
                return self._transcribe_with_faster_whisper(audio)
            else:
                return self._transcribe_with_ctranslate2(audio, max_amp)

        except Exception as e:
            print(f"Error in speech_to_text: {e}")
            return f"Error processing audio: {str(e)}"

    def _transcribe_with_faster_whisper(self, audio):
        """Fine-tuned transcription using faster-whisper medium.en for maximum accuracy"""
        try:
            print("Transcribing with fine-tuned medium.en parameters...")

            # Fine-tuned parameters for maximum accuracy
            segments, info = self.model.transcribe(
                audio,
                language="en",
                beam_size=8,        # Maximum accuracy
                best_of=8,
                patience=1.5,       # Thorough search
                length_penalty=1.0,
                repetition_penalty=1.1,
                no_repeat_ngram_size=4,
                temperature=[0.0, 0.1, 0.2, 0.3],  # Fine-grained temperature schedule
                compression_ratio_threshold=2.2,
                log_prob_threshold=-0.8,    # Higher confidence
                no_speech_threshold=0.5,    # Better speech detection
                condition_on_previous_text=True,
                initial_prompt="This is clear English speech with proper pronunciation and grammar.",
                suppress_blank=True,
                suppress_tokens=[-1],
                word_timestamps=False,      # Faster for real-time
                vad_filter=True,
                vad_parameters=dict(
                    min_silence_duration_ms=300,  # Real-time optimized
                    speech_pad_ms=50,
                    threshold=0.15             # More sensitive
                ),
            )

            # Process segments with confidence tracking
            transcription_parts = []
            total_confidence = 0
            segment_count = 0

            for segment in segments:
                if hasattr(segment, 'avg_logprob') and segment.avg_logprob > -1.5:  # High confidence only
                    transcription_parts.append(segment.text.strip())
                    total_confidence += np.exp(segment.avg_logprob) if segment.avg_logprob > -10 else 0.1
                    segment_count += 1
                elif not hasattr(segment, 'avg_logprob'):  # Fallback if no confidence score
                    transcription_parts.append(segment.text.strip())
                    segment_count += 1

            transcription = " ".join(transcription_parts).strip()
            avg_confidence = total_confidence / segment_count if segment_count > 0 else 0.5

            print(f"Transcription complete: {segment_count} segments, confidence: {avg_confidence:.3f}")
            print(f"Result: '{transcription}'")

            if not transcription:
                return "Could not detect clear speech. Please speak more clearly."

            # Apply optimized post-processing
            cleaned_transcription = self._clean_transcription(transcription)
            final_result = cleaned_transcription if cleaned_transcription else transcription

            # Additional confidence-based filtering
            if len(final_result.split()) < 2 and avg_confidence < 0.3:
                return "Speech was unclear. Please try speaking more clearly."

            return final_result

        except Exception as e:
            print(f"Error in transcription: {e}")
            if "CUDA out of memory" in str(e):
                return "GPU memory full. Please try shorter audio clips."
            return f"Transcription failed: {str(e)}"

    def _transcribe_with_ctranslate2(self, audio, max_amp=None):
        """Transcribe using CTranslate2 (fallback method)"""
        try:
            print("Using CTranslate2 for transcription...")

            # Calculate max_amp if not provided
            if max_amp is None:
                max_amp = np.max(np.abs(audio))

            # Extract features using the processor
            features = self.processor(
                audio,
                sampling_rate=self.sample_rate,
                return_tensors="np"
            )

            # Get the input features for CTranslate2
            input_features = features.input_features[0]

            # Multi-strategy CTranslate2 transcription for better accuracy
            transcription_attempts = []

            # Strategy 1: Optimized beam search with CTranslate2
            try:
                print("CTranslate2 Strategy 1: Optimized beam search...")

                # CTranslate2 transcription with enhanced parameters
                results = self.model.transcribe(
                    input_features,
                    beam_size=3,  # Optimized for GTX 1650
                    best_of=3,
                    patience=1.2,
                    length_penalty=1.0,
                    repetition_penalty=1.05,
                    no_repeat_ngram_size=3,
                    temperature=0.0,
                    compression_ratio_threshold=2.2,
                    log_prob_threshold=-0.8,
                    no_speech_threshold=0.5,
                    condition_on_previous_text=True,
                    initial_prompt="This is a clear English speech recording with proper grammar and punctuation.",
                    suppress_blank=True,
                    suppress_tokens=[-1],
                    word_timestamps=True,
                )

                if results and len(results) > 0:
                    # Extract text from CTranslate2 results
                    full_text = ""
                    total_confidence = 0
                    segment_count = 0

                    for result in results:
                        if hasattr(result, 'text'):
                            full_text += result.text
                        elif isinstance(result, dict) and 'text' in result:
                            full_text += result['text']

                        # Calculate confidence from log probability if available
                        if hasattr(result, 'avg_logprob') and result.avg_logprob is not None:
                            confidence = np.exp(result.avg_logprob)
                            total_confidence += confidence
                            segment_count += 1
                        elif isinstance(result, dict) and 'avg_logprob' in result:
                            confidence = np.exp(result['avg_logprob'])
                            total_confidence += confidence
                            segment_count += 1

                    avg_confidence = total_confidence / segment_count if segment_count > 0 else 0.7

                    cleaned_text = self._clean_transcription(full_text.strip())
                    if cleaned_text:
                        transcription_attempts.append({
                            'text': cleaned_text,
                            'confidence': avg_confidence,
                            'method': 'ctranslate2_beam_search',
                            'language_prob': 0.9  # High confidence for distil-whisper-large-v3
                        })
                        print(f"CTranslate2 Beam result: '{cleaned_text}' (conf: {avg_confidence:.3f})")

            except Exception as e:
                print(f"CTranslate2 beam search failed: {e}")

            # Strategy 2: Greedy CTranslate2 decoding for speed
            try:
                print("CTranslate2 Strategy 2: Greedy decoding...")

                # Greedy decoding with CTranslate2
                greedy_results = self.model.transcribe(
                    input_features,
                    beam_size=1,
                    best_of=1,
                    temperature=0.0,
                    patience=0.0,
                    length_penalty=1.0,
                    repetition_penalty=1.0,
                    no_repeat_ngram_size=0,
                    compression_ratio_threshold=2.4,
                    log_prob_threshold=-1.0,
                    no_speech_threshold=0.6,
                    condition_on_previous_text=False,
                    suppress_blank=True,
                    suppress_tokens=[-1],
                )

                if greedy_results and len(greedy_results) > 0:
                    full_text = ""
                    for result in greedy_results:
                        if hasattr(result, 'text'):
                            full_text += result.text
                        elif isinstance(result, dict) and 'text' in result:
                            full_text += result['text']

                    cleaned_text = self._clean_transcription(full_text.strip())

                    if cleaned_text and cleaned_text not in [a['text'] for a in transcription_attempts]:
                        # Greedy typically has lower confidence but faster
                        confidence = 0.5 + 0.2 * (len(cleaned_text) / 50.0)  # Heuristic

                        transcription_attempts.append({
                            'text': cleaned_text,
                            'confidence': min(confidence, 0.7),
                            'method': 'ctranslate2_greedy',
                            'language_prob': 0.8
                        })
                        print(f"CTranslate2 Greedy result: '{cleaned_text}' (conf: {confidence:.3f})")

            except Exception as e:
                print(f"CTranslate2 greedy decoding failed: {e}")

            # Strategy 3: High-accuracy transcription with temperature sampling
            try:
                print("CTranslate2 Strategy 3: Temperature sampling...")

                for temp in [0.2, 0.4]:  # Skip 0.0 as it's already done in beam search
                    temp_results = self.model.transcribe(
                        input_features,
                        beam_size=2,
                        best_of=2,
                        temperature=temp,
                        length_penalty=1.0,
                        repetition_penalty=1.05,
                        no_repeat_ngram_size=2,
                        compression_ratio_threshold=2.2,
                        log_prob_threshold=-0.8,
                        no_speech_threshold=0.5,
                        condition_on_previous_text=True,
                        patience=1.0,
                        suppress_blank=True,
                        suppress_tokens=[-1],
                    )

                    if temp_results and len(temp_results) > 0:
                        full_text = ""
                        for result in temp_results:
                            if hasattr(result, 'text'):
                                full_text += result.text
                            elif isinstance(result, dict) and 'text' in result:
                                full_text += result['text']

                        cleaned_text = self._clean_transcription(full_text.strip())

                        if cleaned_text and cleaned_text not in [a['text'] for a in transcription_attempts]:
                            # Calculate confidence based on temperature
                            base_confidence = 0.6 - (temp * 0.15)  # Lower temp = higher confidence

                            transcription_attempts.append({
                                'text': cleaned_text,
                                'confidence': base_confidence,
                                'method': f'ctranslate2_temp_{temp}',
                                'language_prob': 0.8
                            })
                            print(f"CTranslate2 Temp {temp} result: '{cleaned_text}' (conf: {base_confidence:.3f})")

            except Exception as e:
                print(f"CTranslate2 temperature sampling failed: {e}")

            # Strategy 4: Conservative fallback for difficult audio
            if len(transcription_attempts) == 0:
                try:
                    print("CTranslate2 Strategy 4: Conservative fallback...")

                    # Very conservative parameters for difficult audio
                    conservative_results = self.model.transcribe(
                        input_features,
                        beam_size=2,
                        best_of=2,
                        temperature=0.1,
                        length_penalty=1.0,
                        repetition_penalty=1.0,
                        no_repeat_ngram_size=0,
                        compression_ratio_threshold=2.5,
                        log_prob_threshold=-0.5,
                        no_speech_threshold=0.3,
                        condition_on_previous_text=False,  # Disable context for difficult audio
                        suppress_blank=True,
                        suppress_tokens=[-1],
                    )

                    if conservative_results and len(conservative_results) > 0:
                        full_text = ""
                        for result in conservative_results:
                            if hasattr(result, 'text'):
                                full_text += result.text
                            elif isinstance(result, dict) and 'text' in result:
                                full_text += result['text']

                        cleaned_text = self._clean_transcription(full_text.strip())

                        if cleaned_text:
                            transcription_attempts.append({
                                'text': cleaned_text,
                                'confidence': 0.4,
                                'method': 'ctranslate2_conservative',
                                'language_prob': 0.6
                            })
                            print(f"CTranslate2 Conservative result: '{cleaned_text}'")

                except Exception as e:
                    print(f"CTranslate2 conservative transcription failed: {e}")

            # Select the best transcription using enhanced criteria
            if not transcription_attempts:
                if max_amp < 0.01:
                    return "The audio was too quiet for processing. Please speak louder."
                else:
                    return "Could not detect clear speech. Please speak more clearly."

            # Enhanced selection criteria for CTranslate2
            def ctranslate2_score(attempt):
                base_score = attempt['confidence']

                # Bonus for longer, more substantial text
                length_bonus = min(len(attempt['text']) / 50.0, 0.2)

                # Bonus for language probability
                lang_bonus = attempt.get('language_prob', 0.5) * 0.1

                # Penalty for very short responses (likely false positives)
                if len(attempt['text']) < 5:
                    length_penalty = -0.3
                else:
                    length_penalty = 0
                
                return base_score + length_bonus + lang_bonus + length_penalty

            # Sort by CTranslate2-optimized score
            best_transcription = max(transcription_attempts, key=ctranslate2_score)
            final_text = best_transcription['text']

            if not final_text:
                return "Could not detect clear speech. Please try speaking more clearly."

            # Final validation
            if len(final_text) < 3:
                return "The speech was too short for reliable processing. Please speak longer phrases."

            final_score = ctranslate2_score(best_transcription)
            print(f"Best CTranslate2 transcription ({best_transcription['method']}): '{final_text}'")
            print(f"Score: {final_score:.3f}, Confidence: {best_transcription['confidence']:.3f}")

            return final_text

        except Exception as e:
            print(f"Error in CTranslate2 speech_to_text: {e}")
            if "CUDA out of memory" in str(e):
                raise RuntimeError("GPU memory exhausted during processing. Please try again.")
            elif "ctranslate2" in str(e).lower():
                raise RuntimeError(f"CTranslate2 processing failed: {str(e)}")
            raise RuntimeError(f"Speech-to-text processing failed: {str(e)}")

    def _adaptive_beam_search(self, audio, initial_beam_size=5):
        """
        Adaptive beam search optimization for CTC decoding
        """
        try:
            best_result = None
            best_score = -float('inf')
            
            current_beam_size = initial_beam_size
            patience_counter = 0
            
            for attempt in range(3):  # Maximum 3 attempts
                print(f"Adaptive CTC attempt {attempt + 1} with beam size {current_beam_size}")
                
                # Configure parameters for this attempt
                adaptive_params = self.transcription_params.copy()
                adaptive_params.update({
                    'beam_size': current_beam_size,
                    'best_of': min(current_beam_size, 5),
                    'temperature': 0.0 if attempt == 0 else 0.1 * attempt
                })
                
                try:
                    segments, info = self.model.transcribe(audio, **adaptive_params)
                    segments_list = list(segments)
                    
                    if segments_list:
                        # Calculate comprehensive score
                        full_text = "".join(segment.text for segment in segments_list)
                        cleaned_text = self._clean_transcription(full_text.strip())
                        
                        if cleaned_text:
                            # Calculate adaptive score
                            avg_logprob = np.mean([seg.avg_logprob for seg in segments_list 
                                                 if hasattr(seg, 'avg_logprob')])
                            confidence = np.exp(avg_logprob) if avg_logprob > -10 else 0.1
                            
                            # Adaptive scoring
                            score = (
                                confidence * 0.6 +
                                (len(cleaned_text) / 100.0) * 0.2 +
                                (1.0 - info.no_speech_prob if hasattr(info, 'no_speech_prob') else 0.5) * 0.2
                            )
                            
                            if score > best_score:
                                improvement = score - best_score
                                best_score = score
                                best_result = {
                                    'text': cleaned_text,
                                    'confidence': confidence,
                                    'score': score,
                                    'beam_size': current_beam_size
                                }
                                
                                # Check if improvement is significant
                                if improvement < self.adaptive_beam_params['score_improvement_threshold']:
                                    patience_counter += 1
                                else:
                                    patience_counter = 0
                                
                                print(f"New best result: '{cleaned_text}' (score: {score:.3f})")
                            else:
                                patience_counter += 1
                
                except Exception as e:
                    print(f"Adaptive beam attempt {attempt + 1} failed: {e}")
                    patience_counter += 1
                
                # Early stopping if no improvement
                if patience_counter >= self.adaptive_beam_params['patience_threshold']:
                    print("Early stopping: no significant improvement")
                    break
                
                # Adjust beam size for next attempt
                if best_result is None:
                    current_beam_size = min(
                        current_beam_size + self.adaptive_beam_params['beam_size_delta'],
                        self.adaptive_beam_params['max_beam_size']
                    )
                else:
                    # If we have a result, try smaller beam for efficiency
                    current_beam_size = max(
                        current_beam_size - 1,
                        self.adaptive_beam_params['min_beam_size']
                    )
            
            return best_result
            
        except Exception as e:
            print(f"Adaptive beam search failed: {e}")
            return None

    def _ctc_postprocess_segments(self, segments):
        """
        Post-process CTC segments for better accuracy
        """
        try:
            if not segments:
                return ""
            
            # Combine segments with intelligent spacing
            processed_text = ""
            
            for i, segment in enumerate(segments):
                text = segment.text.strip()
                if not text:
                    continue
                
                # Add spacing logic
                if i > 0 and processed_text:
                    # Check if we need space
                    if (processed_text[-1].isalnum() and text[0].isalnum()):
                        processed_text += " "
                    elif processed_text[-1] in ".,!?;:" and text[0].isalnum():
                        processed_text += " "
                
                processed_text += text
            
            return processed_text
            
        except Exception as e:
            print(f"CTC segment post-processing failed: {e}")
            return " ".join(segment.text for segment in segments)

    def get_audio_info(self):
        """Get information about audio setup with CTC optimization details"""
        try:
            devices = sd.query_devices()
            default_device = sd.default.device
            
            # Get model information
            model_info = {
                'sample_rate': self.sample_rate,
                'model_size': self.model_size,
                'device': self.device,
                'compute_type': self.compute_type,
                'audio_devices': len(devices),
                'default_device': default_device,
                'ctc_optimized': True,
                'faster_whisper': True
            }
            
            # Add CTC-specific parameters
            model_info.update({
                'ctc_beam_width': self.ctc_params['beam_width'],
                'adaptive_beam_range': f"{self.adaptive_beam_params['min_beam_size']}-{self.adaptive_beam_params['max_beam_size']}",
                'vad_enabled': self.transcription_params.get('vad_filter', False),
                'word_timestamps': self.transcription_params.get('word_timestamps', False)
            })
            
            return model_info
            
        except Exception as e:
            return {'error': str(e), 'ctc_optimized': False}

    def benchmark_ctc_performance(self, test_audio_duration=3):
        """
        Benchmark CTC performance with the current configuration
        """
        try:
            print("Running CTC performance benchmark...")
            
            # Generate test audio (silence for benchmarking)
            test_audio = np.random.normal(0, 0.01, int(test_audio_duration * self.sample_rate)).astype(np.float32)
            
            # Benchmark different strategies
            strategies = [
                ('ctc_beam_search', self.transcription_params),
                ('ctc_greedy', {**self.transcription_params, 'beam_size': 1, 'best_of': 1}),
                ('ctc_conservative', {**self.transcription_params, 'beam_size': 3, 'temperature': 0.1})
            ]
            
            benchmark_results = {}
            
            for strategy_name, params in strategies:
                try:
                    start_time = time.time()
                    
                    # Run transcription
                    segments, info = self.model.transcribe(test_audio, **params)
                    list(segments)  # Force evaluation
                    
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # Calculate metrics
                    real_time_factor = processing_time / test_audio_duration
                    
                    benchmark_results[strategy_name] = {
                        'processing_time': processing_time,
                        'real_time_factor': real_time_factor,
                        'audio_duration': test_audio_duration,
                        'efficiency': 'Fast' if real_time_factor < 0.5 else 'Medium' if real_time_factor < 1.0 else 'Slow'
                    }
                    
                    print(f"{strategy_name}: {processing_time:.2f}s ({real_time_factor:.2f}x real-time)")
                    
                except Exception as e:
                    benchmark_results[strategy_name] = {'error': str(e)}
                    print(f"{strategy_name}: Failed - {e}")
            
            return benchmark_results
            
        except Exception as e:
            print(f"CTC benchmark failed: {e}")
            return {'error': str(e)}

    def optimize_for_hardware(self):
        """
        Optimize CTC parameters based on current hardware
        """
        try:
            print("Optimizing CTC parameters for current hardware...")
            
            # Test hardware capabilities
            if self.device == "cuda":
                # GPU optimization
                try:
                    import torch
                    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                    print(f"GPU Memory: {gpu_memory:.1f} GB")
                    
                    if gpu_memory > 8:
                        # High-end GPU
                        self.transcription_params.update({
                            'beam_size': 8,
                            'best_of': 8,
                            'temperature': [0.0, 0.1, 0.2]
                        })
                        self.ctc_params['beam_width'] = 150
                        print("Applied high-end GPU optimizations")
                        
                    elif gpu_memory > 4:
                        # Mid-range GPU
                        self.transcription_params.update({
                            'beam_size': 5,
                            'best_of': 5
                        })
                        self.ctc_params['beam_width'] = 100
                        print("Applied mid-range GPU optimizations")
                        
                    else:
                        # Low-end GPU
                        self.transcription_params.update({
                            'beam_size': 3,
                            'best_of': 3
                        })
                        self.ctc_params['beam_width'] = 50
                        print("Applied low-end GPU optimizations")
                        
                except Exception as e:
                    print(f"GPU optimization failed: {e}")
                    
            else:
                # CPU optimization
                import psutil
                cpu_count = psutil.cpu_count(logical=False)
                memory_gb = psutil.virtual_memory().total / 1024**3
                
                print(f"CPU Cores: {cpu_count}, Memory: {memory_gb:.1f} GB")
                
                if cpu_count >= 8 and memory_gb >= 16:
                    # High-end CPU
                    self.transcription_params.update({
                        'beam_size': 5,
                        'best_of': 3
                    })
                    self.ctc_params['beam_width'] = 80
                    print("Applied high-end CPU optimizations")
                    
                elif cpu_count >= 4 and memory_gb >= 8:
                    # Mid-range CPU
                    self.transcription_params.update({
                        'beam_size': 3,
                        'best_of': 2
                    })
                    self.ctc_params['beam_width'] = 50
                    print("Applied mid-range CPU optimizations")
                    
                else:
                    # Low-end CPU
                    self.transcription_params.update({
                        'beam_size': 1,
                        'best_of': 1
                    })
                    self.ctc_params['beam_width'] = 30
                    print("Applied low-end CPU optimizations")
            
            return True
            
        except Exception as e:
            print(f"Hardware optimization failed: {e}")
            return False

    def set_ctc_language_model(self, language_model_path=None):
        """
        Set external language model for CTC post-processing
        """
        try:
            if language_model_path:
                # This would integrate with external language models
                # For now, we'll use the built-in language processing
                print(f"Language model integration not implemented yet: {language_model_path}")
                return False
            
            # Use built-in language processing
            if LANGUAGE_TOOLS_AVAILABLE:
                print("Using built-in language processing for CTC post-processing")
                return True
            else:
                print("No language processing tools available")
                return False
                
        except Exception as e:
            print(f"Language model setup failed: {e}")
            return False

    def get_ctc_statistics(self):
        """
        Get CTC processing statistics
        """
        try:
            stats = {
                'model_type': 'faster-whisper',
                'ctc_optimized': True,
                'device': self.device,
                'compute_type': self.compute_type,
                'model_size': self.model_size,
                'sample_rate': self.sample_rate,
                'beam_search_enabled': self.transcription_params.get('beam_size', 1) > 1,
                'vad_enabled': self.transcription_params.get('vad_filter', False),
                'word_timestamps': self.transcription_params.get('word_timestamps', False),
                'language_processing': LANGUAGE_TOOLS_AVAILABLE
            }
            
            return stats
            
        except Exception as e:
            return {'error': str(e)}

# Example usage and testing
if __name__ == "__main__":
    try:
        print("Initializing Whisper Medium.en Speech Processor...")
        processor = SpeechProcessor()

        print("\n=== Speech Processor Ready ===")
        print("Optimized for maximum accuracy with Whisper medium.en")
        print("Fine-tuned for GTX 1650 (4GB VRAM)")
        print("\nUsage:")
        print("  processor.record_audio(duration=10)  # Record audio")
        print("  processor.speech_to_text(audio)      # Transcribe audio")
        print("\nFor real-time usage, try:")
        print("  python stt_pipeline.py")
        print("  python chatbot_pipeline.py")

    except Exception as e:
        print(f"Failed to initialize Speech Processor: {e}")
        print("Please ensure faster-whisper is installed:")
        print("  pip install faster-whisper==1.0.3")