# Whisper Medium.en for GTX 1650

High-accuracy local Speech-to-Text optimized for NVIDIA GTX 1650 (4GB VRAM) using faster-whisper with medium.en model in mixed INT8/FP16 precision.

## Features

- **High Accuracy**: Uses Whisper medium.en model for best English transcription quality that fits in 4GB VRAM
- **GTX 1650 Optimized**: INT8/FP16 mixed precision for optimal memory usage
- **Voice Activity Detection**: Built-in VAD to reduce hallucinations
- **Grammar Correction**: Optional offline (LanguageTool) or API-based (OpenAI) grammar correction
- **Real-time Ready**: Optimized parameters for low latency

## Quick Start

### 1. Installation

```bash
# Run the automated installer
python install_whisper_medium.py

# Or install manually:
pip install --upgrade torch --index-url https://download.pytorch.org/whl/cu118
pip install faster-whisper==1.0.3 soundfile==0.12.1 numpy==1.26.4 sounddevice scipy noisereduce

# Optional grammar correction:
pip install language-tool-python==2.7.1  # Offline (heavier, uses Java)
pip install openai==1.44.0               # API-based
```

### 2. Test Installation

```bash
python test_whisper_medium.py
```

### 3. Basic Usage

```bash
# Record audio
python quick_record.py

# Transcribe audio file
python stt_pipeline.py sample.wav

# Full pipeline with grammar correction
python chatbot_pipeline.py sample.wav
```

## Files Overview

- `stt_pipeline.py` - Core STT functionality with medium.en
- `quick_record.py` - Simple audio recording utility
- `grammar_offline.py` - Offline grammar correction using LanguageTool
- `grammar_api.py` - API-based grammar correction using OpenAI
- `chatbot_pipeline.py` - Complete pipeline: Audio → STT → Grammar → Output
- `speech_processor.py` - Updated main processor (replaces distil-whisper)

## Configuration

### Environment Variables

```bash
# Model selection (default: medium.en)
export WHISPER_MODEL=medium.en        # or small.en for faster processing
export WHISPER_COMPUTE_TYPE=int8_float16  # or float16 if you have more VRAM

# Grammar correction method
export GRAMMAR_METHOD=offline         # offline, api, or simple

# OpenAI API (if using API grammar correction)
export OPENAI_API_KEY=your_api_key_here
```

### Model Options for GTX 1650

| Model | VRAM Usage | Speed | Accuracy | Recommended |
|-------|------------|-------|----------|-------------|
| `small.en` | ~2GB | Fast | Good | For real-time |
| `medium.en` | ~3.5GB | Medium | Excellent | **Default** |
| `large-v3` | ~5GB+ | Slow | Best | Too large for GTX 1650 |

## Performance Tips

### For Best Accuracy
- Use `medium.en` model (default)
- Set `beam_size=5-8` for offline processing
- Enable VAD filtering
- Use temperature schedule `[0.0, 0.2, 0.4]`

### For Best Speed
- Use `small.en` model
- Set `beam_size=1-3`
- Disable word timestamps
- Reduce VAD sensitivity

### Memory Management
- Keep audio clips under 30 seconds
- Use `int8_float16` compute type
- Set `num_workers=1` for predictable memory usage

## API Reference

### Basic Transcription

```python
from stt_pipeline import transcribe_audio

result = transcribe_audio(
    "audio.wav",
    language="en",
    beam_size=5,
    vad=True,
    word_timestamps=False
)

print(result["text"])
```

### Full Pipeline

```python
from chatbot_pipeline import process_audio_to_reply

result = process_audio_to_reply("audio.wav")
print("Raw:", result["transcript_raw"])
print("Corrected:", result["transcript_corrected"])
```

### Grammar Correction

```python
# Offline correction
from grammar_offline import grammar_correct_offline
corrected = grammar_correct_offline("he go to store")

# API correction  
from grammar_api import grammar_correct_api
corrected = grammar_correct_api("he go to store")
```

## Troubleshooting

### CUDA Out of Memory
- Switch to `small.en` model
- Reduce `beam_size` to 1-3
- Use shorter audio clips
- Restart Python to clear GPU memory

### Poor Accuracy
- Ensure good audio quality (16kHz, mono, low noise)
- Increase `beam_size` to 8-10
- Enable VAD filtering
- Use grammar correction

### Slow Performance
- Use `small.en` model
- Reduce `beam_size`
- Disable word timestamps
- Use greedy decoding (`beam_size=1`)

## Hardware Requirements

- **GPU**: NVIDIA GTX 1650 (4GB VRAM) or better
- **RAM**: 8GB+ system RAM recommended
- **Python**: 3.9-3.11 (3.10 recommended)
- **CUDA**: 11.8 or compatible

## Comparison with Previous Setup

| Feature | Previous (distil-whisper-v3) | New (medium.en) |
|---------|------------------------------|-----------------|
| Model Size | Large (~6GB) | Medium (~3.5GB) |
| VRAM Usage | High (float16) | Optimized (int8_float16) |
| Accuracy | Good | Excellent |
| GTX 1650 Fit | Tight | Comfortable |
| Language | Multilingual | English-only |
| Speed | Fast | Balanced |

The new setup provides better accuracy while using less VRAM, making it ideal for GTX 1650 cards.
