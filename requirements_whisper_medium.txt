# Requirements for Whisper Medium.en optimized for GTX 1650
# Python 3.9–3.11 recommended

# PyTorch CUDA (install separately with correct CUDA version)
# pip install --upgrade torch --index-url https://download.pytorch.org/whl/cu118

# High-performance Whisper + utilities
faster-whisper==1.0.3
soundfile==0.12.1
numpy==1.26.4
sounddevice

# Optional: offline grammar suggestions (no API) – heavier, uses Java
# language-tool-python==2.7.1

# If you plan to call OpenAI (or another API) for grammar:
# openai==1.44.0

# Additional dependencies for audio processing
scipy
noisereduce
