# stt_pipeline.py
from faster_whisper import WhisperModel
import soundfile as sf
import numpy as np
import os

# -------- Model choice tips ----------
# "medium.en"  -> highest accuracy that still fits a 4GB GPU using int8_float16
# "small.en"   -> faster, still good accuracy if you need lower latency
MODEL_NAME = os.getenv("WHISPER_MODEL", "medium.en")

# compute_type options:
#   "int8_float16" -> great accuracy/VRAM balance for 4GB cards (GTX 1650)
#   "float16"      -> a bit more VRAM; try only if you don't get OOM
COMPUTE_TYPE = os.getenv("WHISPER_COMPUTE_TYPE", "int8_float16")

# Create model once; reuse across requests
# device="cuda" to use your GTX 1650
model = WhisperModel(
    MODEL_NAME,
    device="cuda",
    compute_type=COMPUTE_TYPE,
    num_workers=1,      # keep memory usage predictable
    cpu_threads=0       # 0 = let runtime pick
)

def transcribe_audio(
    audio_path: str,
    language: str = "en",
    beam_size: int = 5,
    vad: bool = True,
    word_timestamps: bool = False,
):
    """
    Transcribe a single audio file with high accuracy on GTX 1650.
    Returns: dict with text, segments (list), and info.
    """
    # ---- Recommended decoding settings for accuracy ----
    # beam_size=5 is a sweet spot; increase to 8–10 for a bit more accuracy if you can afford time.
    # temperature=[0.0, 0.2, 0.4] helps escape bad decodes on tough audio.
    temperature = [0.0, 0.2, 0.4]

    vad_params = dict(
        min_silence_duration_ms=500, # split on pauses; reduce to 300 for faster turn-taking
    ) if vad else None

    segments, info = model.transcribe(
        audio_path,
        language=language,
        beam_size=beam_size,
        temperature=temperature,
        vad_filter=vad,
        vad_parameters=vad_params,
        word_timestamps=word_timestamps,
        # Speed/accuracy knobs:
        no_speech_threshold=0.6,            # ignore low-energy noise
        log_prob_threshold=-1.0,             # discard very low confidence text
        compression_ratio_threshold=2.4,     # resample weird output
    )

    out_text = []
    out_segments = []
    for seg in segments:
        out_text.append(seg.text)
        out_segments.append({
            "start": seg.start,
            "end": seg.end,
            "text": seg.text
        })
    return {
        "language": info.language,
        "duration": info.duration,
        "text": " ".join(out_text).strip(),
        "segments": out_segments
    }

if __name__ == "__main__":
    # Example usage
    audio_file = "sample.wav"  # 16 kHz mono preferred, but faster-whisper accepts many formats
    result = transcribe_audio(audio_file, word_timestamps=False)
    print("DETECTED LANGUAGE:", result["language"])
    print("DURATION (s):", result["duration"])
    print("TRANSCRIPT:\n", result["text"])
