# Migration Guide: From Distil-Whisper to Medium.en

This guide helps you migrate from the previous faster-distil-whisperv3 setup to the new Whisper Medium.en optimized for GTX 1650.

## What Changed

### Model Upgrade
- **Old**: Systran/faster-distil-whisper-large-v3 (multilingual, ~6GB VRAM)
- **New**: medium.en (English-only, ~3.5GB VRAM with int8_float16)

### Key Improvements
- ✅ Better accuracy for English speech
- ✅ Lower VRAM usage (fits comfortably on GTX 1650)
- ✅ More stable memory usage
- ✅ Optimized parameters for GTX 1650
- ✅ Enhanced grammar correction pipeline
- ✅ Better error handling

## Migration Steps

### 1. Backup Current Setup (Optional)
```bash
# Backup your current speech_processor.py if you made custom changes
cp speech_processor.py speech_processor_backup.py
```

### 2. Install New Dependencies
```bash
# Run the automated installer
python install_whisper_medium.py

# Or install manually
pip install --upgrade torch --index-url https://download.pytorch.org/whl/cu118
pip install faster-whisper==1.0.3 soundfile==0.12.1 numpy==1.26.4
```

### 3. Clean Up Old Models (Optional)
```bash
# Remove old model files to free up disk space
python cleanup_old_models.py
```

### 4. Test New Setup
```bash
# Test the new installation
python test_whisper_medium.py

# Test with your existing audio files
python chatbot_pipeline.py your_audio_file.wav
```

## Code Changes Required

### If You Import SpeechProcessor Directly

**Old Code:**
```python
from speech_processor import SpeechProcessor

processor = SpeechProcessor()
# This still works, but now uses medium.en instead of distil-whisper
```

**New Code (Recommended):**
```python
# Use the new optimized pipeline
from stt_pipeline import transcribe_audio
from chatbot_pipeline import process_audio_to_reply

# For simple transcription
result = transcribe_audio("audio.wav")
print(result["text"])

# For full pipeline with grammar correction
result = process_audio_to_reply("audio.wav")
print(result["transcript_corrected"])
```

### Environment Variables

**New Options:**
```bash
# Model selection (default: medium.en)
export WHISPER_MODEL=medium.en        # or small.en for speed
export WHISPER_COMPUTE_TYPE=int8_float16

# Grammar correction
export GRAMMAR_METHOD=offline         # offline, api, or simple
export OPENAI_API_KEY=your_key        # if using API grammar
```

## Performance Comparison

| Metric | Old (distil-whisper-v3) | New (medium.en) | Improvement |
|--------|-------------------------|-----------------|-------------|
| VRAM Usage | ~5-6GB (float16) | ~3.5GB (int8_float16) | 30-40% less |
| Accuracy (English) | Good | Excellent | Significantly better |
| Speed | Fast | Balanced | Slightly slower but more accurate |
| Memory Stability | Variable | Predictable | More stable |
| GTX 1650 Compatibility | Tight fit | Comfortable | Much better |

## Troubleshooting Migration Issues

### "Model not found" Error
The new setup downloads medium.en automatically on first use. This is normal.

### CUDA Out of Memory
If you still get OOM errors:
```bash
export WHISPER_MODEL=small.en  # Use smaller model
```

### Import Errors
Make sure you installed the new dependencies:
```bash
pip install -r requirements_whisper_medium.txt
```

### Performance Issues
The new setup is optimized for accuracy over speed. For faster processing:
```python
# In your code, use smaller beam size
result = transcribe_audio("audio.wav", beam_size=3)
```

## Rollback Instructions

If you need to rollback to the old setup:

1. Restore your backup:
```bash
cp speech_processor_backup.py speech_processor.py
```

2. Reinstall old dependencies:
```bash
pip install transformers ctranslate2 huggingface_hub
```

3. The old model files should still be in your cache unless you ran the cleanup script.

## New Features Available

### Grammar Correction
```python
# Offline grammar correction (no API needed)
from grammar_offline import grammar_correct_offline
corrected = grammar_correct_offline("he go to store")

# API-based correction (requires OpenAI key)
from grammar_api import grammar_correct_api
corrected = grammar_correct_api("he go to store")
```

### Enhanced Audio Recording
```python
# Simple recording utility
from quick_record import record_to_wav
audio_file = record_to_wav("test.wav", seconds=10)
```

### Complete Pipeline
```python
# One-line audio processing
from chatbot_pipeline import process_audio_file
result = process_audio_file("audio.wav")
print(result)  # Fully processed and corrected transcript
```

## Support

If you encounter issues during migration:

1. Run the test script: `python test_whisper_medium.py`
2. Check the README: `README_whisper_medium.md`
3. Verify your GPU has enough VRAM (4GB minimum)
4. Ensure you're using Python 3.9-3.11

The new setup is designed to be more reliable and provide better results on GTX 1650 hardware while using less memory.
