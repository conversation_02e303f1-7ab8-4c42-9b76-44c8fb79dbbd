# chatbot_pipeline.py
from stt_pipeline import transcribe_audio
import os

# Choose grammar correction method
GRAMMAR_METHOD = os.getenv("GRAMMAR_METHOD", "offline")  # "offline", "api", or "simple"

if GRAMMAR_METHOD == "offline":
    try:
        from grammar_offline import grammar_correct_offline as grammar_fix
        print("Using offline grammar correction (LanguageTool)")
    except ImportError:
        print("LanguageTool not available, falling back to simple correction")
        from grammar_api import grammar_correct_simple_api as grammar_fix
elif GRAMMAR_METHOD == "api":
    try:
        from grammar_api import grammar_correct_api as grammar_fix
        print("Using API-based grammar correction (OpenAI)")
    except ImportError:
        print("OpenAI not available, falling back to simple correction")
        from grammar_api import grammar_correct_simple_api as grammar_fix
else:
    from grammar_api import grammar_correct_simple_api as grammar_fix
    print("Using simple grammar correction")

def process_audio_to_reply(audio_path: str) -> dict:
    """
    Complete pipeline: Audio -> STT -> Grammar Correction -> Final Reply
    
    Args:
        audio_path: Path to audio file
        
    Returns:
        dict: Contains raw transcript, corrected transcript, segments, and duration
    """
    try:
        # Step 1: Speech-to-Text
        print("Step 1: Transcribing audio...")
        stt_result = transcribe_audio(audio_path, word_timestamps=False)
        
        if not stt_result["text"]:
            return {
                "transcript_raw": "",
                "transcript_corrected": "No speech detected in audio.",
                "segments": [],
                "duration": stt_result.get("duration", 0),
                "error": "No speech detected"
            }
        
        # Step 2: Grammar Correction
        print("Step 2: Applying grammar correction...")
        corrected = grammar_fix(stt_result["text"])
        
        return {
            "transcript_raw": stt_result["text"],
            "transcript_corrected": corrected,
            "segments": stt_result["segments"],
            "duration": stt_result["duration"],
            "language": stt_result.get("language", "en")
        }
        
    except Exception as e:
        print(f"Error in processing pipeline: {e}")
        return {
            "transcript_raw": "",
            "transcript_corrected": f"Error processing audio: {str(e)}",
            "segments": [],
            "duration": 0,
            "error": str(e)
        }

def process_audio_file(audio_path: str, verbose: bool = True) -> str:
    """
    Simple interface that returns just the corrected transcript.
    
    Args:
        audio_path: Path to audio file
        verbose: Whether to print processing steps
        
    Returns:
        str: Corrected transcript
    """
    result = process_audio_to_reply(audio_path)
    
    if verbose:
        print(f"\nProcessing: {audio_path}")
        print(f"Duration: {result.get('duration', 0):.1f}s")
        print(f"Language: {result.get('language', 'unknown')}")
        
        if result.get('transcript_raw'):
            print(f"\nRAW TRANSCRIPT:")
            print(f"'{result['transcript_raw']}'")
        
        print(f"\nFINAL RESULT:")
        print(f"'{result['transcript_corrected']}'")
    
    return result["transcript_corrected"]

if __name__ == "__main__":
    import sys
    
    # Check if audio file is provided as argument
    if len(sys.argv) > 1:
        audio_file = sys.argv[1]
    else:
        audio_file = "sample.wav"  # Default file
    
    if not os.path.exists(audio_file):
        print(f"Audio file '{audio_file}' not found.")
        print("Usage: python chatbot_pipeline.py <audio_file>")
        print("Or record audio first: python quick_record.py")
        sys.exit(1)
    
    # Process the audio file
    result = process_audio_to_reply(audio_file)
    
    print("\n" + "="*50)
    print("AUDIO PROCESSING RESULTS")
    print("="*50)
    print(f"File: {audio_file}")
    print(f"Duration: {result.get('duration', 0):.1f} seconds")
    print(f"Language: {result.get('language', 'unknown')}")
    
    if result.get('error'):
        print(f"Error: {result['error']}")
    else:
        print(f"\nRAW TRANSCRIPT:")
        print(f"'{result['transcript_raw']}'")
        
        print(f"\nCORRECTED TRANSCRIPT:")
        print(f"'{result['transcript_corrected']}'")
        
        if result.get('segments'):
            print(f"\nSEGMENTS ({len(result['segments'])}):")
            for i, seg in enumerate(result['segments'][:5]):  # Show first 5 segments
                print(f"  {i+1}. [{seg['start']:.1f}s-{seg['end']:.1f}s]: {seg['text']}")
            if len(result['segments']) > 5:
                print(f"  ... and {len(result['segments']) - 5} more segments")
    
    print("="*50)
