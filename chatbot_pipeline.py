# chatbot_pipeline.py
from stt_pipeline import transcribe_audio
import os
import re

# Simple built-in grammar correction (no external dependencies)
def simple_grammar_fix(text: str) -> str:
    """
    Built-in grammar correction without external APIs or heavy dependencies
    """
    if not text or not text.strip():
        return text

    # Basic corrections for common speech-to-text errors
    corrections = {
        # Common contractions
        r'\bi\b': 'I',  # Always capitalize "I"
        r'\bim\b': "I'm",
        r'\bdont\b': "don't",
        r'\bcant\b': "can't",
        r'\bwont\b': "won't",
        r'\byoure\b': "you're",
        r'\btheyre\b': "they're",
        r'\bwere\b': "we're",
        r'\bitsnt\b': "isn't",
        r'\barent\b': "aren't",
        r'\bwasnt\b': "wasn't",
        r'\bwerent\b': "weren't",
        r'\bhasnt\b': "hasn't",
        r'\bhavent\b': "haven't",
        r'\bhadnt\b': "hadn't",
        r'\bwill not\b': "won't",
        r'\bcan not\b': "can't",
        r'\bdo not\b': "don't",

        # Common word corrections
        r'\bu\b': 'you',
        r'\bur\b': 'your',
        r'\bthere\b(?=\s+is|are)': 'there',
        r'\btheir\b(?=\s+is|are)': 'there',

        # Fix multiple spaces
        r'\s+': ' ',

        # Fix punctuation spacing
        r'\s+([.!?])': r'\1',
        r'([.!?])\s*([a-zA-Z])': r'\1 \2',
    }

    corrected = text
    for pattern, replacement in corrections.items():
        corrected = re.sub(pattern, replacement, corrected, flags=re.IGNORECASE)

    # Capitalize first letter of sentences
    corrected = re.sub(r'(^|[.!?]\s+)([a-z])', lambda m: m.group(1) + m.group(2).upper(), corrected)

    # Ensure first character is capitalized
    if corrected and corrected[0].islower():
        corrected = corrected[0].upper() + corrected[1:]

    return corrected.strip()

# Choose grammar correction method
GRAMMAR_METHOD = os.getenv("GRAMMAR_METHOD", "offline")  # "offline" or "simple"

if GRAMMAR_METHOD == "offline":
    try:
        from grammar_offline import grammar_correct_offline as grammar_fix
        print("Using offline grammar correction (LanguageTool)")
    except ImportError:
        print("LanguageTool not available, using built-in simple correction")
        grammar_fix = simple_grammar_fix
else:
    grammar_fix = simple_grammar_fix
    print("Using built-in simple grammar correction")

def process_audio_to_reply(audio_path: str) -> dict:
    """
    Complete pipeline: Audio -> STT -> Grammar Correction -> Final Reply
    
    Args:
        audio_path: Path to audio file
        
    Returns:
        dict: Contains raw transcript, corrected transcript, segments, and duration
    """
    try:
        # Step 1: Speech-to-Text
        print("Step 1: Transcribing audio...")
        stt_result = transcribe_audio(audio_path, word_timestamps=False)
        
        if not stt_result["text"]:
            return {
                "transcript_raw": "",
                "transcript_corrected": "No speech detected in audio.",
                "segments": [],
                "duration": stt_result.get("duration", 0),
                "error": "No speech detected"
            }
        
        # Step 2: Grammar Correction
        print("Step 2: Applying grammar correction...")
        corrected = grammar_fix(stt_result["text"])
        
        return {
            "transcript_raw": stt_result["text"],
            "transcript_corrected": corrected,
            "segments": stt_result["segments"],
            "duration": stt_result["duration"],
            "language": stt_result.get("language", "en")
        }
        
    except Exception as e:
        print(f"Error in processing pipeline: {e}")
        return {
            "transcript_raw": "",
            "transcript_corrected": f"Error processing audio: {str(e)}",
            "segments": [],
            "duration": 0,
            "error": str(e)
        }

def process_audio_file(audio_path: str, verbose: bool = True) -> str:
    """
    Simple interface that returns just the corrected transcript.
    
    Args:
        audio_path: Path to audio file
        verbose: Whether to print processing steps
        
    Returns:
        str: Corrected transcript
    """
    result = process_audio_to_reply(audio_path)
    
    if verbose:
        print(f"\nProcessing: {audio_path}")
        print(f"Duration: {result.get('duration', 0):.1f}s")
        print(f"Language: {result.get('language', 'unknown')}")
        
        if result.get('transcript_raw'):
            print(f"\nRAW TRANSCRIPT:")
            print(f"'{result['transcript_raw']}'")
        
        print(f"\nFINAL RESULT:")
        print(f"'{result['transcript_corrected']}'")
    
    return result["transcript_corrected"]

if __name__ == "__main__":
    import sys
    
    # Check if audio file is provided as argument
    if len(sys.argv) > 1:
        audio_file = sys.argv[1]
    else:
        audio_file = "sample.wav"  # Default file
    
    if not os.path.exists(audio_file):
        print(f"Audio file '{audio_file}' not found.")
        print("Usage: python chatbot_pipeline.py <audio_file>")
        print("Or record audio first: python quick_record.py")
        sys.exit(1)
    
    # Process the audio file
    result = process_audio_to_reply(audio_file)
    
    print("\n" + "="*50)
    print("AUDIO PROCESSING RESULTS")
    print("="*50)
    print(f"File: {audio_file}")
    print(f"Duration: {result.get('duration', 0):.1f} seconds")
    print(f"Language: {result.get('language', 'unknown')}")
    
    if result.get('error'):
        print(f"Error: {result['error']}")
    else:
        print(f"\nRAW TRANSCRIPT:")
        print(f"'{result['transcript_raw']}'")
        
        print(f"\nCORRECTED TRANSCRIPT:")
        print(f"'{result['transcript_corrected']}'")
        
        if result.get('segments'):
            print(f"\nSEGMENTS ({len(result['segments'])}):")
            for i, seg in enumerate(result['segments'][:5]):  # Show first 5 segments
                print(f"  {i+1}. [{seg['start']:.1f}s-{seg['end']:.1f}s]: {seg['text']}")
            if len(result['segments']) > 5:
                print(f"  ... and {len(result['segments']) - 5} more segments")
    
    print("="*50)
